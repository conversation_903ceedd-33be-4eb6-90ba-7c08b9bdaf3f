import $ from 'jquery';

// Mock getQueryParam
jest.mock('../../site/scripts/utils/params.util', () => ({
  getQueryParam: jest.fn(),
}));

// Mock BaseComponent
jest.mock('../../site/scripts/base.ts', () => ({
  BaseComponent: class { },
}));

// Mock slick-carousel
jest.mock('slick-carousel', () => ({}));

import { getQueryParam } from '../../site/scripts/utils/params.util';
import { CardTypeFilterComponent } from '../../site/scripts/credit-card-hub';

describe('CardTypeFilterComponent', () => {
  beforeEach(() => {
    document.body.innerHTML = `
      <tcb-credit-card-hub>
        <div class="promotion-hub_group-credit-card">
          <div data-promotion-hub-card-type="credit,business" class="promotion-hub_credit-card"></div>
          <div data-promotion-hub-card-type="debit" class="promotion-hub_credit-card"></div>
          <div data-promotion-hub-card-type="" class="promotion-hub_credit-card empty"></div>
          <div class="promotion-hub_credit-card no-type"></div>
        </div>
      </tcb-credit-card-hub>
    `;

    // Mock jQuery methods
    $.fn.slick = jest.fn();
    $.fn.hasClass = jest.fn();
    $.fn.hide = jest.fn();
    $.fn.show = jest.fn();
    $.fn.filter = jest.fn().mockReturnThis();
    $.fn.is = jest.fn().mockReturnValue(true);

    // Mock window object
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should do nothing if no query param', () => {
    getQueryParam.mockReturnValue(null);

    const filter = new CardTypeFilterComponent();

    // expect no DOM change
    const cards = $('[data-promotion-hub-card-type]');
    expect(cards.length).toBe(3);
  });

  test('should remove unmatched cards and show matched ones', () => {
    getQueryParam.mockReturnValue('credit,debit');

    const removeSpy = jest.spyOn($.fn, 'remove');
    const cssSpy = jest.spyOn($.fn, 'css');

    const filter = new CardTypeFilterComponent();

    // One card should be shown (credit,business)
    expect(cssSpy).toHaveBeenCalledWith('display', 'flex');

    // One empty-type and one no-match should be removed
    setTimeout(() => {
      expect(removeSpy).toHaveBeenCalledTimes(2);
    }, 10);
  });

  test('should match slug with the-tin-dung/ and the-thanh-toan/', () => {
    getQueryParam.mockReturnValue('the-thanh-toan/business');

    const removeSpy = jest.spyOn($.fn, 'remove');
    const cssSpy = jest.spyOn($.fn, 'css');

    const filter = new CardTypeFilterComponent();

    expect(cssSpy).toHaveBeenCalledWith('display', 'flex'); // matched via prefix
    expect(removeSpy).toHaveBeenCalledTimes(2); // 2 others removed
  });

  test('should remove cards with no data attribute or empty type', () => {
    getQueryParam.mockReturnValue('credit');

    const removeSpy = jest.spyOn($.fn, 'remove');

    new CardTypeFilterComponent();

    // Two cards missing or having empty data-promotion-hub-card-type should be removed
    expect(removeSpy).toHaveBeenCalledTimes(2);
  });

  test('should initialize carousel on mobile screen size', () => {
    getQueryParam.mockReturnValue(null);

    // Mock mobile screen size
    Object.defineProperty(window, 'innerWidth', {
      value: 767,
      writable: true
    });

    // Mock jQuery width method
    $.fn.width = jest.fn().mockReturnValue(767);
    $(window).width = jest.fn().mockReturnValue(767);
    $.fn.hasClass = jest.fn().mockReturnValue(false);

    new CardTypeFilterComponent();

    expect($.fn.slick).toHaveBeenCalledWith({
      slidesToShow: 1,
      slidesToScroll: 1,
      dots: true,
      arrows: false,
      infinite: false,
      mobileFirst: true,
      adaptiveHeight: true,
      responsive: [
        {
          breakpoint: 768,
          settings: 'unslick'
        }
      ]
    });
  });

  test('should not initialize carousel on desktop screen size', () => {
    getQueryParam.mockReturnValue(null);

    // Mock desktop screen size
    Object.defineProperty(window, 'innerWidth', {
      value: 1024,
      writable: true
    });

    // Mock jQuery width method
    $.fn.width = jest.fn().mockReturnValue(1024);
    $(window).width = jest.fn().mockReturnValue(1024);
    $.fn.hasClass = jest.fn().mockReturnValue(false);

    new CardTypeFilterComponent();

    expect($.fn.slick).not.toHaveBeenCalled();
  });

  test('should destroy carousel when resizing from mobile to desktop', () => {
    getQueryParam.mockReturnValue(null);

    // Mock desktop screen size
    $(window).width = jest.fn().mockReturnValue(1024);
    $.fn.hasClass = jest.fn().mockReturnValue(true); // carousel is initialized

    const component = new CardTypeFilterComponent();

    // Trigger resize event manually
    component.configCarousel();

    expect($.fn.slick).toHaveBeenCalledWith('unslick');
  });

  test('should hide tcb-credit-card-hub when no cards are visible', () => {
    getQueryParam.mockReturnValue('nonexistent-type');

    // Mock that no cards are visible
    $.fn.filter = jest.fn().mockReturnValue({ length: 0 });

    new CardTypeFilterComponent();

    expect($.fn.hide).toHaveBeenCalled();
  });

  test('should show tcb-credit-card-hub when cards are visible', () => {
    getQueryParam.mockReturnValue(null);

    // Mock that cards are visible
    $.fn.filter = jest.fn().mockReturnValue({ length: 2 });

    new CardTypeFilterComponent();

    expect($.fn.show).toHaveBeenCalled();
  });

  test('should hide hub when all cards are filtered out', () => {
    getQueryParam.mockReturnValue('nonmatching-filter');

    // Mock that all cards get removed/hidden
    $.fn.filter = jest.fn().mockReturnValue({ length: 0 });

    new CardTypeFilterComponent();

    expect($.fn.hide).toHaveBeenCalled();
  });
});
