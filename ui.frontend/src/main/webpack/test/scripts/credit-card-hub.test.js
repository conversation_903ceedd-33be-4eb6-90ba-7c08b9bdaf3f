import $ from 'jquery';

// Mock getQueryParam
jest.mock('../../site/scripts/utils/params.util', () => ({
  getQueryParam: jest.fn(),
}));

// Mock BaseComponent
jest.mock('../../site/scripts/base.ts', () => ({
  BaseComponent: class { },
}));

// Mock slick-carousel
jest.mock('slick-carousel', () => ({}));

import { getQueryParam } from '../../site/scripts/utils/params.util';
import { CardTypeFilterComponent } from '../../site/scripts/credit-card-hub';

describe('CardTypeFilterComponent', () => {
  beforeEach(() => {
    document.body.innerHTML = `
      <div class="promotion-hub_group-credit-card">
        <div data-promotion-hub-card-type="credit,business" class="card"></div>
        <div data-promotion-hub-card-type="debit" class="card"></div>
        <div data-promotion-hub-card-type="" class="card empty"></div>
        <div class="card no-type"></div>
      </div>
    `;

    // Mock jQuery slick method
    $.fn.slick = jest.fn();
    $.fn.hasClass = jest.fn();

    // Mock window object
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should do nothing if no query param', () => {
    getQueryParam.mockReturnValue(null);

    const filter = new CardTypeFilterComponent();

    // expect no DOM change
    const cards = $('[data-promotion-hub-card-type]');
    expect(cards.length).toBe(3);
  });

  test('should remove unmatched cards and show matched ones', () => {
    getQueryParam.mockReturnValue('credit,debit');

    const removeSpy = jest.spyOn($.fn, 'remove');
    const cssSpy = jest.spyOn($.fn, 'css');

    const filter = new CardTypeFilterComponent();

    // One card should be shown (credit,business)
    expect(cssSpy).toHaveBeenCalledWith('display', 'flex');

    // One empty-type and one no-match should be removed
    setTimeout(() => {
      expect(removeSpy).toHaveBeenCalledTimes(2);
    }, 10);
  });

  test('should match slug with the-tin-dung/ and the-thanh-toan/', () => {
    getQueryParam.mockReturnValue('the-thanh-toan/business');

    const removeSpy = jest.spyOn($.fn, 'remove');
    const cssSpy = jest.spyOn($.fn, 'css');

    const filter = new CardTypeFilterComponent();

    expect(cssSpy).toHaveBeenCalledWith('display', 'flex'); // matched via prefix
    expect(removeSpy).toHaveBeenCalledTimes(2); // 2 others removed
  });

  test('should remove cards with no data attribute or empty type', () => {
    getQueryParam.mockReturnValue('credit');

    const removeSpy = jest.spyOn($.fn, 'remove');

    new CardTypeFilterComponent();

    // Two cards missing or having empty data-promotion-hub-card-type should be removed
    expect(removeSpy).toHaveBeenCalledTimes(2);
  });

  test('should initialize carousel on mobile screen size', () => {
    getQueryParam.mockReturnValue(null);

    // Mock mobile screen size
    Object.defineProperty(window, 'innerWidth', {
      value: 767,
      writable: true
    });

    // Mock jQuery width method
    $.fn.width = jest.fn().mockReturnValue(767);
    $(window).width = jest.fn().mockReturnValue(767);
    $.fn.hasClass = jest.fn().mockReturnValue(false);

    new CardTypeFilterComponent();

    expect($.fn.slick).toHaveBeenCalledWith({
      slidesToShow: 1,
      slidesToScroll: 1,
      dots: true,
      arrows: false,
      infinite: false,
      mobileFirst: true,
      adaptiveHeight: true,
      responsive: [
        {
          breakpoint: 768,
          settings: 'unslick'
        }
      ]
    });
  });

  test('should not initialize carousel on desktop screen size', () => {
    getQueryParam.mockReturnValue(null);

    // Mock desktop screen size
    Object.defineProperty(window, 'innerWidth', {
      value: 1024,
      writable: true
    });

    // Mock jQuery width method
    $.fn.width = jest.fn().mockReturnValue(1024);
    $(window).width = jest.fn().mockReturnValue(1024);
    $.fn.hasClass = jest.fn().mockReturnValue(false);

    new CardTypeFilterComponent();

    expect($.fn.slick).not.toHaveBeenCalled();
  });

  test('should destroy carousel when resizing from mobile to desktop', () => {
    getQueryParam.mockReturnValue(null);

    // Mock desktop screen size
    $(window).width = jest.fn().mockReturnValue(1024);
    $.fn.hasClass = jest.fn().mockReturnValue(true); // carousel is initialized

    const component = new CardTypeFilterComponent();

    // Trigger resize event manually
    component.configCarousel();

    expect($.fn.slick).toHaveBeenCalledWith('unslick');
  });
});
