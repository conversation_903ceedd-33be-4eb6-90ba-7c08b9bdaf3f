import 'slick-carousel';
import { getQueryParam } from './utils/params.util';
import { BaseComponent } from './base';
import { screenSmMax } from './constants/common';

export class CardTypeFilterComponent extends BaseComponent {
  constructor() {
    super();
    this.init();
    this.initCarousel();
  }

  init() {
    this.$cardType = $('[data-promotion-hub-card-type]');
    const params = getQueryParam('card-types');
    if (!params || !this.$cardType.length) {
      this.checkHubVisibility();
      return;
    }

    const paramSet = new Set(params.split(','));
    this.filterCards(paramSet);
    this.checkHubVisibility();
  }

  filterCards(paramSet) {
    this.$cardType.each(function () {
      const $card = $(this);
      const types = $card.data('promotion-hub-card-type');

      if (!types) {
        $card.remove();
        return;
      }

      const typeArr = types.split(',');
      const isMatched = typeArr.some(
        (slug) =>
          paramSet.has(`${slug}`) || paramSet.has(`the-tin-dung/${slug}`) || paramSet.has(`the-thanh-toan/${slug}`),
      );

      if (!isMatched) {
        $card.remove();
      } else {
        $card.css('display', 'flex');
      }
    });
  }

  checkHubVisibility() {
    const $visibleCards = $('.promotion-hub_credit-card').filter(function() {
      return $(this).css('display') !== 'none' && $(this).is(':visible');
    });

    const $hubElement = $('tcb-credit-card-hub');

    if ($visibleCards.length === 0) {
      $hubElement.hide();
    } else {
      $hubElement.show();
    }
  }

  initCarousel() {
    const self = this;

    // Initialize carousel on page load if needed
    this.configCarousel();

    // Handle window resize
    $(window).on('resize', function() {
      self.configCarousel();
    });
  }

  configCarousel() {
    const $carouselContainer = $('.promotion-hub_group-credit-card');

    if ($carouselContainer.length === 0) {
      return;
    }

    if ($(window).width() <= screenSmMax) {
      // Initialize carousel for mobile
      if (!$carouselContainer.hasClass('slick-initialized')) {
        $carouselContainer.slick({
          slidesToShow: 1.1,
          slidesToScroll: 1,
          dots: true,
          arrows: false,
          infinite: false,
          mobileFirst: true,
          adaptiveHeight: true,
          responsive: [
            {
              breakpoint: 768,
              settings: 'unslick'
            }
          ]
        });
      }
    } else {
      // Destroy carousel for desktop
      if ($carouselContainer.hasClass('slick-initialized')) {
        $carouselContainer.slick('unslick');
      }
    }
  }
}
